package desk

import (
	"fwyytool/api/arkgo"

	"github.com/gin-gonic/gin"
)

var ArkDataDiffService arkDataDiffService

type arkDataDiffService struct {
}

func (s arkDataDiffService) GetDiffRes(ctx *gin.Context, timeRange int64, handler string, startTime, endTime int64) (data arkgo.GetArkStudentDataDiffResp, err error) {
	params := arkgo.GetArkStudentDataDiffResParams{
		TimeRange: timeRange,
		Handler:   handler,
		StartTime: startTime, // 新增：支持自定义时间范围
		EndTime:   endTime,   // 新增：支持自定义时间范围
	}

	arkTestCaseDetail, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, params)
	if err != nil {
		return
	}
	return arkTestCaseDetail, nil
}
