package arkgo

import (
	"fwyytool/stru"
)

type GetArkStudentDataDiffResParams struct {
	TimeRange int64  `json:"timeRange"`
	Handler   string `json:"handler"`
	StartTime int64  `json:"startTime,omitempty"` // 新增：自定义开始时间（可选）
	EndTime   int64  `json:"endTime,omitempty"`   // 新增：自定义结束时间（可选）
}

type GetDiffCountParams struct {
	StartTime int64 `json:"startTime,omitempty"` // 开始时间（可选）
	EndTime   int64 `json:"endTime,omitempty"`   // 结束时间（可选）
}

type GetArkDetailByCnameParams struct {
	Cname   string `json:"cname"`
	IsInner int    `json:"isInner"`
}

type GetArkDetailByRuleIdParams struct {
	RuleId  int64 `json:"ruleId"`
	IsInner int   `json:"isInner"`
}

type GetArkFeatureToolDetailByToolIdParams struct {
	ToolId       int64  `json:"toolId"`
	ComponentKey string `json:"componentKey"`
	IsInner      int    `json:"isInner"`
}

type GetArkStudentDataDiffResp struct {
	HasDiffNum     int64                                     `json:"hasDiffNum"`
	NoDiffNum      int64                                     `json:"noDiffNum"`
	UnFinishedTask int64                                     `json:"unFinishedTask"`
	Total          int64                                     `json:"total"`
	DataDiffList   []*GetArkStudentDataDiffResOutPutDataDiff `json:"dataDiffList"`
}

type GetDataDiffReportOutPut struct {
	Handler        string `json:"handler"`
	HasDiffNum     int64  `json:"hasDiffNum"`
	NoDiffNum      int64  `json:"noDiffNum"`
	UnFinishedTask int64  `json:"unFinishedTask"`
}

type GetArkStudentDataDiffResOutPutDataDiff struct {
	ID          int64  `json:"id"` // 记录ID
	Params      string `json:"params"`
	DiffNum     int    `json:"diffNum"`
	OldData     string `json:"oldData"`
	NewData     string `json:"newData"`
	DiffResult  string `json:"diffResult"` // diff 结果
	HandlerName string `json:"handler_name"`
	Fingerprint string `json:"fingerprint"` // 指纹
	DiffType    int    `json:"diffType"`    // 对比类型：0-新老对比，1-环比对比
	Status      int    `json:"status"`      // 状态：0-未完成，1-已完成，2-失败
	CreateTime  int64  `json:"createTime"`
	UpdateTime  int64  `json:"updateTime"`
}

type GetArkDetailResp struct {
	DetailList []*ArkTestCaseDetail `json:"detailList"`
}

type ArkTestCaseDetail struct {
	RuleId     int64                          `json:"ruleId"`
	RuleKey    string                         `json:"ruleKey"`
	Name       string                         `json:"name"`
	Rule       stru.Rule                      `json:"rule"`
	BindCourse []*ArkTestCaseDetailBindCourse `json:"bindCourse"`
}

type ArkTestCaseDetailBindCourse struct {
	CourseId    int64                                    `json:"courseId"`
	Year        int                                      `json:"year"`
	TplId       int64                                    `json:"tplId"`
	ServiceId   int64                                    `json:"serviceId"`
	ServiceName string                                   `json:"serviceName"`
	TeacherList []ArkTestCaseDetailBindCourseTeacherList `json:"teacherList"`
}

type ArkTestCaseDetailBindCourseTeacherList struct {
	AssistantUid int64 `json:"assistantUid"`
}

// GetArkFeatureToolDetail resp
type GetArkFeatureToolDetailResp struct {
	DetailList []*GetArkFeatureToolDetailOutputRuleDetail `json:"detailList"`
}

type GetArkFeatureToolDetailOutputRuleDetail struct {
	TplId        int64                                      `json:"tplId"`
	ServiceId    int64                                      `json:"serviceId"`
	ServiceName  string                                     `json:"serviceName"`
	ComponentKey string                                     `json:"componentKey"`
	Options      string                                     `json:"options"`
	BindCourse   []*GetArkFeatureToolDetailOutputBindCourse `json:"bindCourse"`
}

type GetArkFeatureToolDetailOutputBindCourse struct {
	CourseId    int64                                                `json:"courseId"`
	Year        int                                                  `json:"year"`
	TplId       int64                                                `json:"tplId"`
	ServiceId   int64                                                `json:"serviceId"`
	ServiceName string                                               `json:"serviceName"`
	TeacherList []GetArkFeatureToolDetailOutputBindCourseTeacherList `json:"teacherList"`
}
type GetArkFeatureToolDetailOutputBindCourseTeacherList struct {
	AssistantUid int64 `json:"assistantUid"`
}

type GetArkFusingResp struct {
	ArkFusingKey       []ArkFusingKey       `json:"arkFusingKey"`
	ArkFusingTimeLimit []ArkFusingTimeLimit `json:"arkFusingTimeLimit"`
}

type ArkFusingTimeLimit struct {
	ArkKey string `json:"arkKey"`
	Times  int64  `json:"times"`
	TTl    int64  `json:"ttl"`
}

type ArkFusingKey struct {
	ArkKey string `json:"arkKey"`
	TTl    int64  `json:"ttl"`
}
type GetRdConfigParams struct {
	Key string `json:"key" form:"key"`
}

type GetDiffRecordsParams struct {
	UpdateTimeRange int64 `json:"updateTimeRange" form:"updateTimeRange"`
	LastId          int64 `json:"lastId" form:"lastId"`
}

type UpdateDiffRecordsParams struct {
	ID      int64                  `json:"id" form:"id"`
	Updates map[string]interface{} `json:"updates" form:"updates"`
}
