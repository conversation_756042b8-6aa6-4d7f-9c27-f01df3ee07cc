package desk

import (
	"fwyytool/api/arkgo"
	deskinput "fwyytool/controllers/http/desk/input"
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"net/http"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
)

var DataDiffController dataDiffController

type dataDiffController struct {
}

func (s dataDiffController) DataDiffDetail(ctx *gin.Context) {
	errMsg := ""
	var params struct {
		TimeRange int64  `json:"timeRange" form:"timeRange"`
		Handler   string `json:"handler" form:"handler"`
		StartTime int64  `json:"startTime" form:"startTime"` // 新增：自定义开始时间
		EndTime   int64  `json:"endTime" form:"endTime"`     // 新增：自定义结束时间
	}

	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkDataDiffService.GetDiffRes(ctx, params.TimeRange, params.Handler, params.StartTime, params.EndTime)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", output)
}

// GetHandlerList 获取可用的handler列表，支持搜索和过滤
//
// 接口路径: GET /fwyytool/desk/datadiff/handlerlist
//
// 功能说明:
// 1. 无搜索条件时：返回简单的handler名称列表，保持向后兼容
// 2. 有搜索条件时：获取详细数据进行过滤，返回符合条件的handler列表
//
// 返回格式:
//
//	{
//	  "errNo": 0,
//	  "errMsg": "",
//	  "data": ["handler1", "handler2", ...]
//	}
func (s dataDiffController) GetHandlerList(ctx *gin.Context) {
	// 绑定搜索参数
	params := &deskinput.GetHandlerListParam{}
	if err := ctx.ShouldBind(params); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 验证参数
	if err := params.Validate(ctx); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 如果没有搜索条件，返回简单的handler列表
	if params.Search == "" && len(params.Status) == 0 && len(params.HandlerNames) == 0 && params.HasDiff == nil {
		resp, err := arkgo.NewClient().GetDiffReportRes(ctx)
		if err != nil {
			ctx.JSON(http.StatusOK, gin.H{
				"errNo":  1,
				"errMsg": err.Error(),
				"data":   nil,
			})
			return
		}

		// 提取handler列表
		handlerList := make([]string, 0, len(resp))
		for handler := range resp {
			handlerList = append(handlerList, handler)
		}

		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  0,
			"errMsg": "",
			"data":   handlerList,
		})
		return
	}

	// 有搜索条件时，需要获取详细数据进行过滤
	diffParams := arkgo.GetArkStudentDataDiffResParams{
		StartTime: params.StartTime,
		EndTime:   params.EndTime,
	}

	diffResp, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, diffParams)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 过滤数据
	filteredData := s.filterDiffData(diffResp.DataDiffList, params)

	// 提取唯一的handler列表
	handlerSet := make(map[string]bool)
	for _, item := range filteredData {
		handlerSet[item.HandlerName] = true
	}

	handlerList := make([]string, 0, len(handlerSet))
	for handler := range handlerSet {
		handlerList = append(handlerList, handler)
	}

	// 排序
	sort.Strings(handlerList)

	ctx.JSON(http.StatusOK, gin.H{
		"errNo":  0,
		"errMsg": "",
		"data":   handlerList,
	})
}

// filterDiffData 根据搜索条件过滤diff数据
//
// 参数说明:
// - dataList: 原始diff数据列表
// - params: 搜索和过滤参数
//
// 过滤逻辑:
// 1. 模糊搜索：在HandlerName、Params、Fingerprint字段中查找匹配内容
// 2. 状态过滤：精确匹配Status字段
// 3. 接口名过滤：精确匹配HandlerName字段
// 4. 差异过滤：根据DiffNum是否大于0判断是否有差异
// 5. 排序：调用sortDiffData进行排序
//
// 返回: 过滤和排序后的数据列表
func (s dataDiffController) filterDiffData(dataList []*arkgo.GetArkStudentDataDiffResOutPutDataDiff, params *deskinput.GetHandlerListParam) []*arkgo.GetArkStudentDataDiffResOutPutDataDiff {
	var filtered []*arkgo.GetArkStudentDataDiffResOutPutDataDiff

	for _, item := range dataList {
		// 模糊搜索：接口名、请求参数
		if params.Search != "" {
			searchTerm := strings.ToLower(params.Search)
			handlerName := strings.ToLower(item.HandlerName)
			itemParams := strings.ToLower(item.Params)
			fingerprint := strings.ToLower(item.Fingerprint)

			if !strings.Contains(handlerName, searchTerm) &&
				!strings.Contains(itemParams, searchTerm) &&
				!strings.Contains(fingerprint, searchTerm) {
				continue
			}
		}

		// 状态过滤
		if len(params.Status) > 0 {
			statusMatch := false
			for _, status := range params.Status {
				if item.Status == status {
					statusMatch = true
					break
				}
			}
			if !statusMatch {
				continue
			}
		}

		// 接口名多选过滤
		if len(params.HandlerNames) > 0 {
			handlerMatch := false
			for _, handler := range params.HandlerNames {
				if item.HandlerName == handler {
					handlerMatch = true
					break
				}
			}
			if !handlerMatch {
				continue
			}
		}

		// 是否存在差异过滤
		if params.HasDiff != nil {
			hasDiff := item.DiffNum > 0
			expectedHasDiff := *params.HasDiff == 1
			if hasDiff != expectedHasDiff {
				continue
			}
		}

		filtered = append(filtered, item)
	}

	// 排序
	s.sortDiffData(filtered, params.SortBy, params.SortOrder)

	return filtered
}

// sortDiffData 对diff数据进行排序
//
// 参数说明:
// - dataList: 要排序的diff数据列表
// - sortBy: 排序字段，支持 "createTime"、"updateTime"、"diffNum"
// - sortOrder: 排序方向，"asc" 升序，"desc" 降序
//
// 排序逻辑:
// 1. createTime: 按创建时间排序
// 2. updateTime: 按更新时间排序（默认）
// 3. diffNum: 按差异数量排序
// 4. 默认降序排列，可通过sortOrder参数调整
//
// 注意: 该方法直接修改传入的dataList，无返回值
func (s dataDiffController) sortDiffData(dataList []*arkgo.GetArkStudentDataDiffResOutPutDataDiff, sortBy, sortOrder string) {
	if len(dataList) == 0 {
		return
	}

	sort.Slice(dataList, func(i, j int) bool {
		var less bool
		switch sortBy {
		case "createTime":
			less = dataList[i].CreateTime < dataList[j].CreateTime
		case "updateTime":
			less = dataList[i].UpdateTime < dataList[j].UpdateTime
		case "diffNum":
			less = dataList[i].DiffNum < dataList[j].DiffNum
		default: // 默认按更新时间排序
			less = dataList[i].UpdateTime < dataList[j].UpdateTime
		}

		if sortOrder == "desc" {
			return !less
		}
		return less
	})
}

// SearchDiffData 搜索和过滤diff数据，支持完整的搜索功能
//
// 接口路径: GET /fwyytool/desk/datadiff/search
//
// 功能说明:
// 提供完整的diff数据搜索和过滤功能，支持：
// - 模糊搜索：在接口名、请求参数、指纹中搜索
// - 状态过滤：按任务状态进行多选过滤
// - 接口名过滤：按接口名进行多选过滤
// - 差异过滤：按是否存在差异进行过滤
// - 排序功能：按创建时间、更新时间、差异数排序
// - 时间范围：按时间范围过滤数据
//
// 返回格式:
//
//	{
//	  "errNo": 0,
//	  "errMsg": "",
//	  "data": {
//	    "total": 100,
//	    "dataDiffList": [...],
//	    "hasSearch": true
//	  }
//	}
func (s dataDiffController) SearchDiffData(ctx *gin.Context) {
	// 绑定搜索参数
	params := &deskinput.GetHandlerListParam{}
	if err := ctx.ShouldBind(params); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 验证参数
	if err := params.Validate(ctx); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 获取详细数据
	diffParams := arkgo.GetArkStudentDataDiffResParams{
		StartTime: params.StartTime,
		EndTime:   params.EndTime,
	}

	diffResp, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, diffParams)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 过滤和排序数据
	filteredData := s.filterDiffData(diffResp.DataDiffList, params)

	// 构建响应数据
	responseData := gin.H{
		"total":        len(filteredData),
		"dataDiffList": filteredData,
		"hasSearch":    params.Search != "" || len(params.Status) > 0 || len(params.HandlerNames) > 0 || params.HasDiff != nil,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"errNo":  0,
		"errMsg": "",
		"data":   responseData,
	})
}

// GetDiffCount 获取差异统计数据
func (s dataDiffController) GetDiffCount(ctx *gin.Context) {
	var params struct {
		StartTime int64 `json:"startTime" form:"startTime"` // 开始时间
		EndTime   int64 `json:"endTime" form:"endTime"`     // 结束时间
	}

	if err := ctx.ShouldBind(&params); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 调用arkgo接口获取diff统计数据
	resp, err := arkgo.NewClient().GetDiffCountRes(ctx, arkgo.GetDiffCountParams{
		StartTime: params.StartTime,
		EndTime:   params.EndTime,
	})
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"errNo":  0,
		"errMsg": "",
		"data":   resp,
	})
}
