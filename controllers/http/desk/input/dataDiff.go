package input

import "github.com/gin-gonic/gin"

type DataDiffParam struct {
	HasDiff int64 `json:"hasDiff" form:"hasDiff" binding:"hasDiff"`
}

// GetHandlerListParam 获取Handler列表的搜索参数
//
// 使用示例：
// GET /fwyytool/desk/datadiff/handlerlist?search=CourseList&status=1&status=2&sortBy=updateTime&sortOrder=desc
// GET /fwyytool/desk/datadiff/search?search=test&hasDiff=1&startTime=1640995200&endTime=1641081600
//
// 字段说明：
// - search: 模糊搜索，会在接口名、请求参数、指纹中查找匹配的内容
// - status: 状态过滤，可传入多个值进行多选过滤
// - handlerNames: 接口名过滤，可传入多个接口名进行多选过滤
// - hasDiff: 差异过滤，0表示无差异，1表示有差异，不传表示不过滤
// - sortBy: 排序字段，支持 createTime、updateTime、diffNum
// - sortOrder: 排序方向，asc升序，desc降序，默认desc
// - startTime/endTime: 时间范围过滤，Unix时间戳格式
type GetHandlerListParam struct {
	Search       string   `json:"search" form:"search"`             // 模糊搜索：接口名、请求参数、指纹
	Status       []int    `json:"status" form:"status"`             // 状态过滤：0-未完成，1-已完成，2-失败
	HandlerNames []string `json:"handlerNames" form:"handlerNames"` // 接口名多选过滤
	HasDiff      *int     `json:"hasDiff" form:"hasDiff"`           // 是否存在差异：0-无差异，1-有差异
	SortBy       string   `json:"sortBy" form:"sortBy"`             // 排序字段：createTime, updateTime, diffNum
	SortOrder    string   `json:"sortOrder" form:"sortOrder"`       // 排序方向：asc, desc
	StartTime    int64    `json:"startTime" form:"startTime"`       // 开始时间（Unix时间戳）
	EndTime      int64    `json:"endTime" form:"endTime"`           // 结束时间（Unix时间戳）
}

func (p *GetHandlerListParam) Validate(ctx *gin.Context) error {
	// 设置默认排序
	if p.SortBy == "" {
		p.SortBy = "updateTime"
	}
	if p.SortOrder == "" {
		p.SortOrder = "desc"
	}
	return nil
}
