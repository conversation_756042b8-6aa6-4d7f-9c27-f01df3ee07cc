# 数据Diff详情页面重设计

## 1. Overview

### 设计目标
重新设计fwyytool项目中的数据diff详情页面（`/templates/desk/datadiff/diffdetail.html`），提升用户体验和数据可视化效果。原页面主要用于展示不同接口的数据差异对比结果，服务于运营人员、数据分析师和系统管理员的日常工作。

### 核心问题
- 原有界面设计过于传统，缺乏现代化视觉效果
- 大量数据展示时可读性差，信息密度过高
- 缺乏数据趋势分析和可视化图表
- 用户交互体验不佳，操作效率低下
- 移动端适配性差

### 设计原则
- **数据驱动**: 以清晰的数据展示为核心
- **用户友好**: 简化操作流程，提升使用效率
- **响应式设计**: 支持多设备访问
- **渐进增强**: 保持与现有技术栈的兼容性

## 2. Technology Stack & Dependencies

### 前端技术栈
``mermaid
graph TB
    A[Bootstrap 5.x] --> B[响应式布局框架]
    C[ECharts 5.x] --> D[数据可视化]
    E[jQuery 3.x] --> F[DOM操作与事件处理]
    G[Font Awesome 6.x] --> H[图标库]
    I[Moment.js] --> J[时间处理]
    K[DataTables] --> L[表格增强]
```

### 依赖升级计划
- Bootstrap: 3.x → 5.x (现代化UI组件)
- 新增ECharts: 数据可视化图表库
- 新增DataTables: 高级表格功能
- 保持jQuery兼容性

### 技术兼容性
- 保持Go Template语法不变
- 后端API接口保持现有结构
- 渐进式升级，确保向后兼容

## 3. Component Architecture

### 组件层次结构
``mermaid
graph TD
    A[DiffDetailPage] --> B[HeaderComponent]
    A --> C[MetricsOverview]
    A --> D[QueryPanel]
    A --> E[DataVisualization]
    A --> F[DataTable]
    A --> G[DetailModal]
    
    B --> B1[Breadcrumb]
    B --> B2[PageTitle]
    
    C --> C1[StatCard]
    C --> C2[TrendChart]
    
    D --> D1[TimeRangeSelector]
    D --> D2[HandlerSelector]
    D --> D3[FilterControls]
    
    E --> E1[DiffTrendChart]
    E --> E2[InterfaceDistribution]
    
    F --> F1[DataGrid]
    F --> F2[Pagination]
    F --> F3[ActionButtons]
    
    G --> G1[DiffViewer]
    G --> G2[JsonComparator]
    G --> G3[DetailTabs]
    G --> G4[ExportButton]
```

### 核心组件定义

#### MetricsOverview (数据概览)
```html
<!-- 替代原有的简单统计表格 -->
<div class="metrics-overview">
    <div class="row">
        <div class="col-md-3">
            <div class="metric-card total">
                <div class="metric-icon"><i class="fas fa-tasks"></i></div>
                <div class="metric-value">{{.data.Total}}</div>
                <div class="metric-label">总任务数</div>
                <div class="metric-trend">+12.5%</div>
            </div>
        </div>
        <!-- 其他指标卡片... -->
    </div>
</div>
```

#### QueryPanel (智能查询面板)
```html
<!-- 优化的查询表单 -->
<div class="query-panel collapsed">
    <div class="panel-header">
        <h5>查询条件</h5>
        <button class="toggle-btn">展开/收起</button>
    </div>
    <div class="panel-body">
        <form class="query-form">
            <div class="form-row">
                <div class="col-md-4">
                    <label>时间范围</label>
                    <select class="form-control enhanced-select">
                        <!-- 选项... -->
                    </select>
                </div>
                <!-- 其他查询条件... -->
            </div>
        </form>
    </div>
</div>
```

#### DataVisualization (数据可视化)
```html
<!-- 新增的图表区域 -->
<div class="data-visualization">
    <div class="row">
        <div class="col-md-8">
            <div class="chart-container">
                <canvas id="diffTrendChart"></canvas>
            </div>
        </div>
        <div class="col-md-4">
            <div class="chart-container">
                <canvas id="interfaceDistChart"></canvas>
            </div>
        </div>
    </div>
</div>
```

#### DataTable (增强数据表格)
```html
<!-- 改进的数据表格 -->
<div class="enhanced-datatable">
    <div class="table-controls">
        <div class="table-search">
            <input type="text" placeholder="搜索接口名、指纹...">
        </div>
        <div class="table-filters">
            <select class="status-filter">
                <option value="">全部状态</option>
                <option value="1">已完成</option>
                <option value="0">未完成</option>
                <option value="2">失败</option>
            </select>
            <select class="diff-filter">
                <option value="">全部结果</option>
                <option value="has-diff">有差异</option>
                <option value="no-diff">无差异</option>
            </select>
            <select class="type-filter">
                <option value="">全部类型</option>
                <option value="0">新老对比</option>
                <option value="1">环比对比</option>
            </select>
        </div>
    </div>
    <div class="table-responsive">
        <table id="diffDataTable" class="table table-striped">
            <thead>
                <tr>
                    <th>接口名称</th>
                    <th>请求参数</th>
                    <th>差异数量</th>
                    <th>状态</th>
                    <th>类型</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {{range $key, $diffInfo := .data.DataDiffList}}
                <tr data-id="{{$diffInfo.ID}}" 
                    data-status="{{$diffInfo.Status}}" 
                    data-diff-type="{{$diffInfo.DiffType}}">
                    <td>
                        <span class="handler-name">{{$diffInfo.HandlerName}}</span>
                        <small class="text-muted d-block">{{$diffInfo.Fingerprint}}</small>
                    </td>
                    <td>
                        <div class="params-preview" title="点击查看完整参数">
                            {{truncateJson $diffInfo.Params 50}}
                        </div>
                    </td>
                    <td>
                        <span class="badge {{if gt $diffInfo.DiffNum 0}}badge-danger{{else}}badge-success{{end}}">
                            {{$diffInfo.DiffNum}}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge status-{{$diffInfo.Status}}">
                            {{getStatusText $diffInfo.Status}}
                        </span>
                    </td>
                    <td>
                        <span class="type-badge">
                            {{getDiffTypeText $diffInfo.DiffType}}
                        </span>
                    </td>
                    <td>
                        <span title="{{showTime $diffInfo.CreateTime}}">
                            {{formatRelativeTime $diffInfo.CreateTime}}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="viewDiffDetail({{$diffInfo.ID}})">
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            {{if $diffInfo.DiffResult}}
                            <a href="{{$diffInfo.DiffResult}}" 
                               class="btn btn-sm btn-outline-secondary" 
                               target="_blank">
                                <i class="fas fa-external-link-alt"></i> Diff
                            </a>
                            {{end}}
                        </div>
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>
    <div class="table-pagination">
        <nav aria-label="分页导航">
            <ul class="pagination">
                <!-- 分页内容由JavaScript动态生成 -->
            </ul>
        </nav>
    </div>
</div>
```

#### DetailModal (详情弹窗)
``html
<!-- 数据差异详情弹窗 -->
<div class="modal fade" id="diffDetailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-code-branch"></i>
                    数据差异详情
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="diff-detail-container">
                    <!-- 基本信息 -->
                    <div class="basic-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>接口名称:</td>
                                        <td id="detail-handler-name"></td>
                                    </tr>
                                    <tr>
                                        <td>差异数量:</td>
                                        <td id="detail-diff-num"></td>
                                    </tr>
                                    <tr>
                                        <td>状态:</td>
                                        <td id="detail-status"></td>
                                    </tr>
                                    <tr>
                                        <td>类型:</td>
                                        <td id="detail-diff-type"></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>时间信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>创建时间:</td>
                                        <td id="detail-create-time"></td>
                                    </tr>
                                    <tr>
                                        <td>更新时间:</td>
                                        <td id="detail-update-time"></td>
                                    </tr>
                                    <tr>
                                        <td>指纹:</td>
                                        <td id="detail-fingerprint"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tab导航 -->
                    <ul class="nav nav-tabs" id="diffDetailTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="params-tab" data-toggle="tab" 
                               href="#params" role="tab">请求参数</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="comparison-tab" data-toggle="tab" 
                               href="#comparison" role="tab">数据对比</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="diff-result-tab" data-toggle="tab" 
                               href="#diff-result" role="tab">差异详情</a>
                        </li>
                    </ul>
                    
                    <!-- Tab内容 -->
                    <div class="tab-content" id="diffDetailTabContent">
                        <!-- 请求参数 -->
                        <div class="tab-pane fade show active" id="params" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="detail-params"></pre>
                            </div>
                        </div>
                        
                        <!-- 数据对比 -->
                        <div class="tab-pane fade" id="comparison" role="tabpanel">
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6>旧数据 (Before)</h6>
                                    <div class="json-viewer">
                                        <pre id="detail-old-data"></pre>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>新数据 (After)</h6>
                                    <div class="json-viewer">
                                        <pre id="detail-new-data"></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 差异详情 -->
                        <div class="tab-pane fade" id="diff-result" role="tabpanel">
                            <div class="mt-3">
                                <div class="diff-result-container">
                                    <div id="detail-diff-result"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="export-diff-btn">
                    <i class="fas fa-download"></i> 导出数据
                </button>
            </div>
        </div>
    </div>
</div>
```

## 4. Routing & Navigation

### 页面导航结构
``mermaid
graph LR
    A[首页] --> B[数据管理]
    B --> C[Diff分析]
    C --> D[详情页面]
    D --> D1[概览视图]
    D --> D2[图表视图]
    D --> D3[表格视图]
    D --> D4[详情弹窗]
```

### 面包屑导航
```html
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">首页</a></li>
        <li class="breadcrumb-item"><a href="/desk">运营工具</a></li>
        <li class="breadcrumb-item"><a href="/desk/datadiff">数据Diff</a></li>
        <li class="breadcrumb-item active">详情页面</li>
    </ol>
</nav>
```

### 页面内导航
- 顶部Tab切换：概览、图表、详细数据
- 侧边快速导航：统计信息、异常项目
- 返回顶部按钮
- 快捷键支持

## 5. Styling Strategy

### 设计系统
```css
:root {
    /* 主题色彩 */
    --primary-color: #1890ff;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #f5222d;
    --text-primary: #262626;
    --text-secondary: #8c8c8c;
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 阴影系统 */
    --shadow-card: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-modal: 0 4px 16px rgba(0,0,0,0.15);
    
    /* 圆角系统 */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
}
```

### 组件样式规范
```css
/* 指标卡片样式 */
.metric-card {
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-card);
    padding: var(--spacing-lg);
    transition: transform 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-modal);
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--spacing-xs);
}

.status-indicator.success { background-color: var(--success-color); }
.status-indicator.error { background-color: var(--error-color); }
.status-indicator.warning { background-color: var(--warning-color); }
```

### 响应式设计
```css
/* 移动端适配 */
@media (max-width: 768px) {
    .metrics-overview .metric-card {
        margin-bottom: var(--spacing-md);
    }
    
    .data-visualization .chart-container {
        height: 300px;
    }
    
    .enhanced-datatable {
        font-size: 14px;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
    }
    
    .data-visualization {
        height: 400px;
    }
}
```

## 6. State Management

### 页面状态管理
```javascript
// 全局状态对象
const PageState = {
    // 查询参数状态
    query: {
        timeRange: 1,
        handler: '',
        status: '',        // 新增: 状态筛选
        diffType: '',      // 新增: diff类型筛选
        hasDiff: '',       // 新增: 是否有差异筛选
        page: 1,
        pageSize: 20
    },
    
    // 数据状态
    data: {
        metrics: null,
        diffList: [],
        chartData: null,
        currentDiffDetail: null,  // 新增: 当前查看的详情
        loading: false,
        error: null
    },
    
    // UI状态
    ui: {
        queryPanelCollapsed: true,
        selectedRows: [],
        activeTab: 'overview',
        modalVisible: false,
        currentModalTab: 'params'  // 新增: 弹窗当前Tab
    }
};

// 状态更新方法
const StateManager = {
    updateQuery(newQuery) {
        Object.assign(PageState.query, newQuery);
        this.fetchData();
    },
    
    setLoading(loading) {
        PageState.data.loading = loading;
        this.renderLoading();
    },
    
    setData(data) {
        PageState.data = { ...PageState.data, ...data };
        this.renderData();
    },
    
    // 新增: 设置当前查看的详情
    setCurrentDiffDetail(diffData) {
        PageState.data.currentDiffDetail = diffData;
        this.showDiffDetailModal();
    },
    
    // 新增: 筛选方法
    filterData(filters) {
        const { status, diffType, hasDiff, searchText } = filters;
        let filtered = [...PageState.data.diffList];
        
        if (status) {
            filtered = filtered.filter(item => item.status == status);
        }
        
        if (diffType) {
            filtered = filtered.filter(item => item.diffType == diffType);
        }
        
        if (hasDiff === 'has-diff') {
            filtered = filtered.filter(item => item.diff_num > 0);
        } else if (hasDiff === 'no-diff') {
            filtered = filtered.filter(item => item.diff_num === 0);
        }
        
        if (searchText) {
            const text = searchText.toLowerCase();
            filtered = filtered.filter(item => 
                item.handler_name.toLowerCase().includes(text) ||
                item.fingerprint.toLowerCase().includes(text)
            );
        }
        
        return filtered;
    }
};
```

### 数据流管理
``mermaid
flowchart TD
    A[用户操作] --> B[状态更新]
    B --> C[API请求]
    C --> D[数据处理]
    D --> E[视图更新]
    E --> F[用户反馈]
    
    G[定时刷新] --> C
    H[WebSocket] --> D
```

## 7. API Integration Layer

### 数据模型定义
```javascript
// 对应Go结构体的前端数据模型
const ArkStudentListDataDiff = {
    id: 0,                    // ID int64
    params: '',               // 请求参数 string
    diff_num: 0,              // diff数量 int
    oldData: '',              // 旧数据 string
    newData: '',              // 新数据 string
    diffResult: '',           // diff详情链接 string
    status: 0,                // 状态 int (0:未完成, 1:完成, 2:失败)
    diffType: 0,              // diff类型 int (0:新老diff, 1:环比diff)
    handler_name: '',         // 接口名称 string
    fingerprint: '',          // 指纹 string
    createTime: 0,            // 创建时间 int64 (timestamp)
    updateTime: 0             // 更新时间 int64 (timestamp)
};

// 状态枚举
const DiffStatus = {
    UNFINISHED: 0,
    COMPLETED: 1,
    FAILED: 2
};

// diff类型枚举
const DiffType = {
    OLD_NEW_DIFF: 0,
    PERIOD_DIFF: 1
};
```

### API接口设计
```javascript
// API客户端封装
class DiffAPIClient {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }
    
    // 获取diff统计数据
    async getDiffStats(params) {
        return await this.request('/arkgo/tool/getdiffcount', params);
    }
    
    // 获取diff详情列表
    async getDiffDetails(params) {
        return await this.request('/desk/datadiff/diffdetail', params);
    }
    
    // 通用请求方法
    async request(endpoint, params) {
        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
}
```

### 数据处理层
```javascript
// 数据处理工具
class DataProcessor {
    // 处理指标数据
    static processMetrics(rawData) {
        return {
            total: rawData.Total || 0,
            hasDiff: rawData.HasDiffNum || 0,
            noDiff: rawData.NoDiffNum || 0,
            unfinished: rawData.UnFinishedTask || 0,
            diffRate: ((rawData.HasDiffNum / rawData.Total) * 100).toFixed(1)
        };
    }
    
    // 格式化单条diff数据
    static formatDiffItem(item) {
        return {
            ...item,
            statusText: this.getStatusText(item.status),
            statusClass: this.getStatusClass(item.status),
            diffTypeText: this.getDiffTypeText(item.diffType),
            createTimeFormatted: this.formatTimestamp(item.createTime),
            updateTimeFormatted: this.formatTimestamp(item.updateTime),
            paramsFormatted: this.formatJsonString(item.params),
            oldDataFormatted: this.formatJsonString(item.oldData),
            newDataFormatted: this.formatJsonString(item.newData),
            hasDiff: item.diff_num > 0
        };
    }
    
    // 状态文本映射
    static getStatusText(status) {
        const statusMap = {
            [DiffStatus.UNFINISHED]: '未完成',
            [DiffStatus.COMPLETED]: '已完成',
            [DiffStatus.FAILED]: '失败'
        };
        return statusMap[status] || '未知';
    }
    
    // 状态样式类映射
    static getStatusClass(status) {
        const classMap = {
            [DiffStatus.UNFINISHED]: 'warning',
            [DiffStatus.COMPLETED]: 'success',
            [DiffStatus.FAILED]: 'danger'
        };
        return classMap[status] || 'secondary';
    }
    
    // diff类型文本映射
    static getDiffTypeText(diffType) {
        const typeMap = {
            [DiffType.OLD_NEW_DIFF]: '新老对比',
            [DiffType.PERIOD_DIFF]: '环比对比'
        };
        return typeMap[diffType] || '未知类型';
    }
    
    // 时间戳格式化
    static formatTimestamp(timestamp) {
        if (!timestamp) return '-';
        return new Date(timestamp * 1000).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    // JSON字符串格式化
    static formatJsonString(jsonStr) {
        if (!jsonStr) return '';
        try {
            const parsed = JSON.parse(jsonStr);
            return JSON.stringify(parsed, null, 2);
        } catch (e) {
            return jsonStr;
        }
    }
    
    // 处理图表数据
    static processChartData(diffList) {
        const timeSeriesData = {};
        const interfaceStats = {};
        const statusStats = { completed: 0, failed: 0, unfinished: 0 };
        const typeStats = { oldNewDiff: 0, periodDiff: 0 };
        
        diffList.forEach(item => {
            const date = new Date(item.createTime * 1000).toDateString();
            const handler = item.handler_name;
            
            // 时间序列数据
            if (!timeSeriesData[date]) {
                timeSeriesData[date] = { total: 0, hasDiff: 0, noDiff: 0 };
            }
            timeSeriesData[date].total++;
            if (item.diff_num > 0) {
                timeSeriesData[date].hasDiff++;
            } else {
                timeSeriesData[date].noDiff++;
            }
            
            // 接口统计数据
            if (!interfaceStats[handler]) {
                interfaceStats[handler] = { total: 0, hasDiff: 0, noDiff: 0 };
            }
            interfaceStats[handler].total++;
            if (item.diff_num > 0) {
                interfaceStats[handler].hasDiff++;
            } else {
                interfaceStats[handler].noDiff++;
            }
            
            // 状态统计
            switch (item.status) {
                case DiffStatus.COMPLETED:
                    statusStats.completed++;
                    break;
                case DiffStatus.FAILED:
                    statusStats.failed++;
                    break;
                case DiffStatus.UNFINISHED:
                    statusStats.unfinished++;
                    break;
            }
            
            // 类型统计
            switch (item.diffType) {
                case DiffType.OLD_NEW_DIFF:
                    typeStats.oldNewDiff++;
                    break;
                case DiffType.PERIOD_DIFF:
                    typeStats.periodDiff++;
                    break;
            }
        });
        
        return { 
            timeSeriesData, 
            interfaceStats, 
            statusStats, 
            typeStats 
        };
    }
    
    // 处理表格数据
    static processTableData(diffList) {
        return diffList.map(item => this.formatDiffItem(item));
    }
}
```

### JavaScript交互功能
```javascript
// 页面初始化和交互函数
class DiffDetailPageController {
    constructor() {
        this.apiClient = new DiffAPIClient(window.location.origin);
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInitialData();
        this.initializeCharts();
    }
    
    bindEvents() {
        // 查询表单提交
        document.querySelector('.query-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleQuerySubmit(e);
        });
        
        // 表格筛选
        document.querySelectorAll('.table-filters select').forEach(select => {
            select.addEventListener('change', () => this.handleFilterChange());
        });
        
        // 搜索框
        document.querySelector('.table-search input').addEventListener('input', 
            this.debounce(() => this.handleSearch(), 300)
        );
        
        // 详情弹窗
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="view-detail"]')) {
                this.viewDiffDetail(e.target.dataset.id);
            }
        });
        
        // 导出功能
        document.getElementById('export-diff-btn').addEventListener('click', 
            () => this.exportDiffData()
        );
    }
    
    // 查看diff详情
    async viewDiffDetail(diffId) {
        try {
            StateManager.setLoading(true);
            
            // 从当前数据中查找
            const diffData = PageState.data.diffList.find(item => item.id == diffId);
            if (!diffData) {
                throw new Error('未找到对应的diff数据');
            }
            
            // 格式化数据
            const formattedData = DataProcessor.formatDiffItem(diffData);
            
            // 更新弹窗内容
            this.updateModalContent(formattedData);
            
            // 显示弹窗
            $('#diffDetailModal').modal('show');
            
        } catch (error) {
            ErrorHandler.handle(error, 'viewDiffDetail');
        } finally {
            StateManager.setLoading(false);
        }
    }
    
    // 更新弹窗内容
    updateModalContent(diffData) {
        // 基本信息
        document.getElementById('detail-handler-name').textContent = diffData.handler_name;
        document.getElementById('detail-diff-num').innerHTML = 
            `<span class="badge ${diffData.hasDiff ? 'badge-danger' : 'badge-success'}">${diffData.diff_num}</span>`;
        document.getElementById('detail-status').innerHTML = 
            `<span class="status-badge status-${diffData.statusClass}">${diffData.statusText}</span>`;
        document.getElementById('detail-diff-type').textContent = diffData.diffTypeText;
        document.getElementById('detail-create-time').textContent = diffData.createTimeFormatted;
        document.getElementById('detail-update-time').textContent = diffData.updateTimeFormatted;
        document.getElementById('detail-fingerprint').textContent = diffData.fingerprint;
        
        // Tab内容
        document.getElementById('detail-params').textContent = diffData.paramsFormatted;
        document.getElementById('detail-old-data').textContent = diffData.oldDataFormatted;
        document.getElementById('detail-new-data').textContent = diffData.newDataFormatted;
        
        // 差异结果链接
        if (diffData.diffResult) {
            document.getElementById('detail-diff-result').innerHTML = 
                `<a href="${diffData.diffResult}" target="_blank" class="btn btn-outline-primary">
                    <i class="fas fa-external-link-alt"></i> 查看完整差异报告
                </a>`;
        } else {
            document.getElementById('detail-diff-result').innerHTML = 
                '<p class="text-muted">暂无差异详情</p>';
        }
    }
    
    // 处理查询提交
    async handleQuerySubmit(event) {
        const formData = new FormData(event.target);
        const queryParams = {
            timeRange: formData.get('timeRange'),
            handler: formData.get('handler'),
            status: formData.get('status'),
            diffType: formData.get('diffType')
        };
        
        StateManager.updateQuery(queryParams);
    }
    
    // 处理筛选变化
    handleFilterChange() {
        const filters = {
            status: document.querySelector('.status-filter').value,
            diffType: document.querySelector('.type-filter').value,
            hasDiff: document.querySelector('.diff-filter').value,
            searchText: document.querySelector('.table-search input').value
        };
        
        const filteredData = StateManager.filterData(filters);
        this.updateTable(filteredData);
    }
    
    // 处理搜索
    handleSearch() {
        this.handleFilterChange();
    }
    
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 导出差异数据
    exportDiffData() {
        const currentData = PageState.data.currentDiffDetail;
        if (!currentData) return;
        
        const exportData = {
            基本信息: {
                接口名称: currentData.handler_name,
                差异数量: currentData.diff_num,
                状态: currentData.statusText,
                类型: currentData.diffTypeText,
                创建时间: currentData.createTimeFormatted,
                更新时间: currentData.updateTimeFormatted,
                指纹: currentData.fingerprint
            },
            请求参数: JSON.parse(currentData.params || '{}'),
            旧数据: JSON.parse(currentData.oldData || '{}'),
            新数据: JSON.parse(currentData.newData || '{}'),
            差异链接: currentData.diffResult
        };
        
        // 下载JSON文件
        const blob = new Blob([JSON.stringify(exportData, null, 2)], 
            { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `diff-detail-${currentData.id}-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    // 更新表格
    updateTable(data) {
        const tbody = document.querySelector('#diffDataTable tbody');
        if (!tbody) return;
        
        tbody.innerHTML = data.map(item => `
            <tr data-id="${item.id}" data-status="${item.status}" data-diff-type="${item.diffType}">
                <td>
                    <span class="handler-name">${item.handler_name}</span>
                    <small class="text-muted d-block">${item.fingerprint}</small>
                </td>
                <td>
                    <div class="params-preview" title="点击查看完整参数">
                        ${this.truncateText(item.params, 50)}
                    </div>
                </td>
                <td>
                    <span class="badge ${item.hasDiff ? 'badge-danger' : 'badge-success'}">
                        ${item.diff_num}
                    </span>
                </td>
                <td>
                    <span class="status-badge status-${item.statusClass}">
                        ${item.statusText}
                    </span>
                </td>
                <td>
                    <span class="type-badge">${item.diffTypeText}</span>
                </td>
                <td>
                    <span title="${item.createTimeFormatted}">
                        ${this.formatRelativeTime(item.createTime)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" 
                                data-action="view-detail" data-id="${item.id}">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        ${item.diffResult ? `
                        <a href="${item.diffResult}" 
                           class="btn btn-sm btn-outline-secondary" 
                           target="_blank">
                            <i class="fas fa-external-link-alt"></i> Diff
                        </a>` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    // 文本截断
    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    // 相对时间格式化
    formatRelativeTime(timestamp) {
        const now = Date.now();
        const time = timestamp * 1000;
        const diff = now - time;
        
        const minute = 60 * 1000;
        const hour = 60 * minute;
        const day = 24 * hour;
        
        if (diff < hour) {
            return `${Math.floor(diff / minute)}分钟前`;
        } else if (diff < day) {
            return `${Math.floor(diff / hour)}小时前`;
        } else {
            return `${Math.floor(diff / day)}天前`;
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new DiffDetailPageController();
});
```

### Go Template辅助函数
```go
// 在Go后端需要添加的模板函数
func RegisterTemplateFuncs() {
    // 状态文本映射
    funcs["getStatusText"] = func(status int) string {
        switch status {
        case 0:
            return "未完成"
        case 1:
            return "已完成"
        case 2:
            return "失败"
        default:
            return "未知"
        }
    }
    
    // diff类型文本映射
    funcs["getDiffTypeText"] = func(diffType int) string {
        switch diffType {
        case 0:
            return "新老对比"
        case 1:
            return "环比对比"
        default:
            return "未知类型"
        }
    }
    
    // JSON字符串截断
    funcs["truncateJson"] = func(jsonStr string, maxLen int) string {
        if len(jsonStr) <= maxLen {
            return jsonStr
        }
        return jsonStr[:maxLen] + "..."
    }
    
    // 相对时间格式化
    funcs["formatRelativeTime"] = func(timestamp int64) string {
        now := time.Now().Unix()
        diff := now - timestamp
        
        if diff < 3600 {
            return fmt.Sprintf("%d分钟前", diff/60)
        } else if diff < 86400 {
            return fmt.Sprintf("%d小时前", diff/3600)
        } else {
            return fmt.Sprintf("%d天前", diff/86400)
        }
    }
    
    // JSON格式化显示
    funcs["jsonPrettyStr"] = func(jsonStr string) string {
        var obj interface{}
        if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
            return jsonStr
        }
        
        pretty, err := json.MarshalIndent(obj, "", "  ")
        if err != nil {
            return jsonStr
        }
        
        return string(pretty)
    }
}
```
```javascript
// 错误处理中心
class ErrorHandler {
    static handle(error, context) {
        console.error(`Error in ${context}:`, error);
        
        // 显示用户友好的错误信息
        const message = this.getUserFriendlyMessage(error);
        this.showNotification(message, 'error');
        
        // 上报错误信息（如果需要）
        this.reportError(error, context);
    }
    
    static getUserFriendlyMessage(error) {
        if (error.message.includes('network')) {
            return '网络连接异常，请检查网络设置';
        } else if (error.message.includes('timeout')) {
            return '请求超时，请稍后重试';
        } else {
            return '系统异常，请联系管理员';
        }
    }
    
    static showNotification(message, type = 'info') {
        // 使用Bootstrap Toast或其他通知组件
        const toast = new bootstrap.Toast(document.getElementById('errorToast'));
        document.getElementById('toastMessage').textContent = message;
        toast.show();
    }
}
```

## 8. Testing Strategy

### 单元测试
```javascript
// Jest测试示例
describe('DataProcessor', () => {
    test('should process metrics correctly', () => {
        const rawData = {
            Total: 100,
            HasDiffNum: 15,
            NoDiffNum: 80,
            UnFinishedTask: 5
        };
        
        const result = DataProcessor.processMetrics(rawData);
        
        expect(result.total).toBe(100);
        expect(result.hasDiff).toBe(15);
        expect(result.diffRate).toBe('15.0');
    });
    
    test('should handle empty data gracefully', () => {
        const result = DataProcessor.processMetrics({});
        
        expect(result.total).toBe(0);
        expect(result.diffRate).toBe('NaN');
    });
    
    test('should format diff item correctly', () => {
        const mockItem = {
            id: 1,
            handler_name: 'StudentCallInfo_0',
            diff_num: 3,
            status: 1,
            diffType: 0,
            createTime: 1642742400,
            updateTime: 1642828800,
            params: '{"userId":123}',
            oldData: '{"name":"old"}',
            newData: '{"name":"new"}'
        };
        
        const result = DataProcessor.formatDiffItem(mockItem);
        
        expect(result.statusText).toBe('已完成');
        expect(result.statusClass).toBe('success');
        expect(result.diffTypeText).toBe('新老对比');
        expect(result.hasDiff).toBe(true);
        expect(result.createTimeFormatted).toContain('2022');
    });
});
```

### 集成测试
```javascript
// 页面交互测试
describe('DiffDetailPage Integration', () => {
    beforeEach(() => {
        // 模拟DOM环境
        document.body.innerHTML = '<div id="app"></div>';
        // 初始化页面
        new DiffDetailPage('#app');
    });
    
    test('should load data on page init', async () => {
        // 模拟API响应
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            json: () => Promise.resolve(mockData)
        });
        
        await page.init();
        
        expect(document.querySelector('.metric-card')).toBeTruthy();
    });
    
    test('should update data when query changes', async () => {
        const queryPanel = document.querySelector('.query-panel');
        const timeSelect = queryPanel.querySelector('select[name="timeRange"]');
        
        // 模拟用户选择
        timeSelect.value = '2';
        timeSelect.dispatchEvent(new Event('change'));
        
        // 验证API调用
        expect(global.fetch).toHaveBeenCalledWith(
            expect.stringContaining('/desk/datadiff/diffdetail'),
            expect.objectContaining({
                body: expect.stringContaining('"timeRange":2')
            })
        );
    });
});
```

### 端到端测试
```javascript
// Cypress测试示例
describe('Diff Detail Page E2E', () => {
    beforeEach(() => {
        cy.visit('/desk/datadiff/diffdetail');
    });
    
    it('should display metrics overview', () => {
        cy.get('.metrics-overview').should('be.visible');
        cy.get('.metric-card').should('have.length', 4);
        cy.get('.metric-value').first().should('contain.text', '100');
    });
    
    it('should filter data by time range', () => {
        cy.get('select[name="timeRange"]').select('两天内的数据');
        cy.get('button[type="submit"]').click();
        
        cy.url().should('include', 'timeRange=2');
        cy.get('.table tbody tr').should('have.length.gt', 0);
    });
    
    it('should open diff detail modal', () => {
        cy.get('.table tbody tr').first().find('a').contains('Diff结果').click();
        cy.get('.modal').should('be.visible');
        cy.get('.json-comparator').should('exist');
    });
    
    it('should be responsive on mobile', () => {
        cy.viewport('iphone-x');
        cy.get('.metrics-overview').should('be.visible');
        cy.get('.table-responsive').should('have.css', 'overflow-x', 'auto');
    });
});
```

### 性能测试
```javascript
// 性能监控
class PerformanceMonitor {
    static measureRenderTime(componentName, renderFn) {
        const start = performance.now();
        const result = renderFn();
        const end = performance.now();
        
        console.log(`${componentName} render time: ${end - start}ms`);
        
        // 如果渲染时间超过阈值，记录警告
        if (end - start > 100) {
            console.warn(`Slow render detected for ${componentName}`);
        }
        
        return result;
    }
    
    static measureDataProcessing(dataSize, processFn) {
        const start = performance.now();
        const result = processFn();
        const end = performance.now();
        
        const avgTime = (end - start) / dataSize;
        console.log(`Data processing: ${avgTime.toFixed(2)}ms per item`);
        
        return result;
    }
}